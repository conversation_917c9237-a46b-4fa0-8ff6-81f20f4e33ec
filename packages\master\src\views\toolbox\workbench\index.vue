<template>
  <div class="flex flex-col h-full">
    <div class="px-[20%] py-1">
      <Step :current />
    </div>
    <div class="flex-1 px-4 overflow-hidden">
      <KeepAlive>
        <component :is="currentComponent" ref="componentRef" />
      </KeepAlive>
    </div>
    <div class="py-1 text-center">
      <a-space>
        <a-button v-if="current != 0" @click="setStep('prev')">上一步</a-button>
        <a-button v-if="!isLast" @click="setStep('next')"> 下一步 </a-button>
        <div v-show="current === 2" id="footer"></div>
        <a-button type="primary" ghost :loading="saving" @click="handleSave">
          临时保存
        </a-button>
        <a-button
          v-if="isLast"
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          保存
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { message } from 'ant-design-vue';
import Step from './components/step/index.vue';
import { createTool, queryToolById, updateTool } from '@/master/apis/tools';
import useSteps from './useSteps';
import { createToolContext } from './useTool';
import { isObjectString } from 'shared/utils';

import {
  mapParamsWithName,
  serializeToolParams,
  validateComponent,
} from './utils/saveUtils';

const router = useRouter();
const route = useRoute();
const componentRef = useTemplateRef<any>('componentRef');
const professionalSoftwareId = route.query.professionalSoftwareId as string;
const { current, currentComponent, isLast, next, prev } = useSteps();
const { toolParams } = createToolContext();
// 设置步骤
const setStep = async (type: string) => {
  if (type === 'next') {
    const isValid = await validateComponent(componentRef.value);
    if (!isValid) {
      return;
    }
    next();
  } else if (type === 'prev') {
    prev();
  }
};

// 保存状态
const saving = ref(false);

const handleSave = async () => {
  try {
    saving.value = true;
    // 验证当前步骤
    const isValid = await validateComponent(componentRef.value);
    if (!isValid) {
      return;
    }

    const { inputParams, outputParams, paramsMap } = toolParams.value;

    // 映射参数名称
    const finalInputParams = mapParamsWithName(inputParams, paramsMap);
    const finalOutputParams = mapParamsWithName(outputParams, paramsMap);

    // 序列化参数
    const serializedParams = serializeToolParams(
      toolParams.value,
      finalInputParams,
      finalOutputParams,
      professionalSoftwareId
    );

    // 选择API操作
    const action = route.query.sysDesComponentId ? updateTool : createTool;
    await action(serializedParams);
    message.success('保存成功');
    router.push('/toolbox');
  } catch (error: any) {
    message.error(error.message || '保存失败，请稍后再试');
  } finally {
    saving.value = false;
  }
};

onMounted(async () => {
  const sysDesComponentId = route.query.sysDesComponentId as string;
  toolParams.value.desProfessionalSoftwareId = professionalSoftwareId;
  if (sysDesComponentId) {
    const res = await queryToolById(sysDesComponentId);
    toolParams.value = {
      ...res,
      commands: JSON.parse(res.commands),
      inputParams: JSON.parse(res.inputParams),
      outputParams: JSON.parse(res.outputParams),
      pageStructure: JSON.parse(res.pageStructure),
      paramsMap: JSON.parse(res.paramsMap),
      componentIcon: isObjectString(res.componentIcon)
        ? JSON.parse(res.componentIcon)
        : res.componentIcon,
    };
  }
});
</script>
